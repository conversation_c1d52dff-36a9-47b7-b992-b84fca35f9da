# Projectile System Restructuring Proposal

## Overview
This document outlines a proposed restructuring of the BTR projectile system, drawing lessons from the successful enemy system restructuring. The goal is to reduce complexity while maintaining performance and extending functionality.

## Current Problems Summary

### 1. Component Proliferation (30+ files)
- Multiple overlapping managers
- Scattered configuration across many classes
- Complex initialization dependencies
- Difficult to extend with new projectile types

### 2. State System Limitations
- Only 3 concrete states for all projectile behaviors
- Behavior differences handled through flags rather than proper states
- Missing common projectile patterns (bouncing, piercing, explosive)
- State transitions not well-defined

### 3. Integration Complexity
- Dual projectile systems (main vs EnemySystem)
- Complex radar integration
- Multiple audio/effect managers
- Tight coupling with external systems

### 4. Configuration Challenges
- Settings scattered across multiple components
- Hard-coded behaviors in various places
- Difficult to create new projectile variants
- No centralized projectile definitions

## Proposed Architecture

### 1. Core Entity Structure

#### ProjectileEntity (Base Class)
```csharp
public class ProjectileEntity : MonoBehaviour, IEntity, ICombatEntity
{
    // Core identity and lifecycle
    public ProjectileConfiguration Configuration { get; private set; }
    public ProjectileComponents Components { get; private set; }
    
    // Strategy pattern implementations
    public IMovementStrategy MovementStrategy { get; set; }
    public IDamageStrategy DamageStrategy { get; set; }
    public ILifecycleStrategy LifecycleStrategy { get; set; }
    public IEffectStrategy EffectStrategy { get; set; }
    
    // Performance integration
    public int JobSystemIndex { get; set; }
    public bool IsPooled { get; set; }
}
```

#### ProjectileComponents (Component Container)
```csharp
public class ProjectileComponents
{
    public Rigidbody Rigidbody { get; set; }
    public Timeline Timeline { get; set; }
    public Collider Collider { get; set; }
    public ProjectileRadarComponent RadarComponent { get; set; }
    public ProjectileAudioComponent AudioComponent { get; set; }
    public ProjectileEffectComponent EffectComponent { get; set; }
}
```

### 2. Strategy Pattern Implementation

#### Movement Strategies
```csharp
public interface IMovementStrategy
{
    void Initialize(ProjectileEntity projectile, ProjectileConfiguration config);
    void UpdateMovement(float deltaTime, Vector3 currentPosition, Quaternion currentRotation);
    Vector3 GetTargetPosition();
    bool IsMovementComplete();
}

// Implementations:
// - LinearMovementStrategy: Straight-line movement
// - HomingMovementStrategy: Target-seeking with prediction
// - BouncingMovementStrategy: Ricochet off surfaces
// - CurvedMovementStrategy: Arc or spiral patterns
// - OrbitingMovementStrategy: Circular movement around target
```

#### Damage Strategies
```csharp
public interface IDamageStrategy
{
    void Initialize(ProjectileEntity projectile, ProjectileConfiguration config);
    void ApplyDamage(IDamageable target, Vector3 hitPoint);
    float CalculateDamage(IDamageable target);
    bool ShouldPenetrate(IDamageable target);
}

// Implementations:
// - DirectDamageStrategy: Single target damage
// - AreaDamageStrategy: Splash damage in radius
// - PiercingDamageStrategy: Penetrates multiple targets
// - DoTDamageStrategy: Damage over time effects
// - ShieldBreakingStrategy: Specialized anti-shield damage
```

#### Lifecycle Strategies
```csharp
public interface ILifecycleStrategy
{
    void Initialize(ProjectileEntity projectile, ProjectileConfiguration config);
    void UpdateLifecycle(float deltaTime);
    bool ShouldDestroy();
    void OnDestroy();
}

// Implementations:
// - TimedLifecycleStrategy: Lifetime-based destruction
// - DistanceLifecycleStrategy: Range-based destruction
// - CollisionLifecycleStrategy: Destroyed on impact
// - TargetReachedStrategy: Destroyed when reaching target
// - ManualLifecycleStrategy: Externally controlled
```

#### Effect Strategies
```csharp
public interface IEffectStrategy
{
    void Initialize(ProjectileEntity projectile, ProjectileConfiguration config);
    void OnSpawn(Vector3 position, Quaternion rotation);
    void OnUpdate(Vector3 position, Quaternion rotation);
    void OnHit(Vector3 hitPoint, Vector3 normal, IDamageable target);
    void OnDestroy(Vector3 position);
}

// Implementations:
// - TrailEffectStrategy: Visual trails
// - ExplosionEffectStrategy: Explosion on impact/timeout
// - SoundEffectStrategy: Audio feedback
// - ParticleEffectStrategy: Particle systems
// - LightEffectStrategy: Dynamic lighting
```

### 3. Configuration System

#### ProjectileConfiguration (ScriptableObject)
```csharp
[CreateAssetMenu(menuName = "BTR/Projectiles/Projectile Configuration")]
public class ProjectileConfiguration : ScriptableObject
{
    [Header("Basic Properties")]
    public string projectileName;
    public float damage = 10f;
    public float speed = 50f;
    public float lifetime = 5f;
    public float scale = 1f;
    
    [Header("Strategy Configurations")]
    public MovementStrategyConfig movementConfig;
    public DamageStrategyConfig damageConfig;
    public LifecycleStrategyConfig lifecycleConfig;
    public EffectStrategyConfig effectConfig;
    
    [Header("Performance Settings")]
    public bool useJobSystem = true;
    public bool enableRadarTracking = false;
    public int poolPriority = 0;
    
    [Header("Integration Settings")]
    public string chronosClockKey;
    public bool syncWithMusic = false;
    public LayerMask collisionLayers;
}
```

### 4. Simplified Management

#### ProjectileSystem (Single Manager)
```csharp
public class ProjectileSystem : MonoBehaviour
{
    // Core subsystems
    private ProjectilePool projectilePool;
    private ProjectileJobSystem jobSystem;
    private ProjectileFactory factory;
    
    // Public API
    public ProjectileEntity SpawnProjectile(ProjectileConfiguration config, Vector3 position, Quaternion rotation, Transform target = null);
    public void DestroyProjectile(ProjectileEntity projectile);
    public void RegisterProjectile(ProjectileEntity projectile);
    public void UnregisterProjectile(ProjectileEntity projectile);
    
    // Performance management
    public void UpdateProjectiles(float deltaTime);
    public void OptimizePerformance();
}
```

#### ProjectileFactory (Creation Pattern)
```csharp
public class ProjectileFactory
{
    public ProjectileEntity CreateProjectile(ProjectileConfiguration config)
    {
        var projectile = projectilePool.GetProjectile();
        
        // Configure strategies based on configuration
        projectile.MovementStrategy = CreateMovementStrategy(config.movementConfig);
        projectile.DamageStrategy = CreateDamageStrategy(config.damageConfig);
        projectile.LifecycleStrategy = CreateLifecycleStrategy(config.lifecycleConfig);
        projectile.EffectStrategy = CreateEffectStrategy(config.effectConfig);
        
        return projectile;
    }
}
```

## Migration Strategy

### Phase 1: Foundation (Weeks 1-2)
1. **Create base ProjectileEntity class**
   - Implement IEntity and ICombatEntity interfaces
   - Add basic component management
   - Integrate with existing job system

2. **Implement strategy interfaces**
   - Define all strategy interfaces
   - Create basic implementations for current behaviors
   - Add configuration system foundation

3. **Create ProjectileConfiguration system**
   - Design ScriptableObject structure
   - Create configurations for existing projectile types
   - Add editor tools for configuration creation

### Phase 2: Core Strategies (Weeks 3-4)
1. **Implement movement strategies**
   - LinearMovementStrategy (current basic projectiles)
   - HomingMovementStrategy (current homing projectiles)
   - Integrate with existing job system

2. **Implement damage strategies**
   - DirectDamageStrategy (current damage system)
   - Add support for existing damage calculations
   - Maintain compatibility with IDamageable

3. **Implement lifecycle strategies**
   - TimedLifecycleStrategy (current lifetime system)
   - CollisionLifecycleStrategy (current collision detection)
   - Integrate with existing pooling

### Phase 3: Integration (Weeks 5-6)
1. **Create ProjectileSystem manager**
   - Consolidate existing managers
   - Implement unified API
   - Maintain performance characteristics

2. **Integrate with enemy system**
   - Update ProjectileCombatStrategy
   - Remove duplicate projectile systems
   - Ensure seamless enemy integration

3. **Migrate existing projectiles**
   - Convert current projectile types to new system
   - Create configurations for all variants
   - Test performance and functionality

### Phase 4: Enhancement (Weeks 7-8)
1. **Add advanced strategies**
   - BouncingMovementStrategy
   - AreaDamageStrategy
   - ExplosionEffectStrategy
   - Advanced lifecycle patterns

2. **Optimize performance**
   - Profile new system performance
   - Optimize strategy implementations
   - Enhance job system integration

3. **Add new projectile types**
   - Demonstrate system extensibility
   - Create example advanced projectiles
   - Document creation process

## Benefits of Restructuring

### 1. Reduced Complexity
- Single ProjectileSystem manager instead of multiple managers
- Clear strategy pattern for behaviors
- Centralized configuration system
- Simplified initialization and dependencies

### 2. Enhanced Extensibility
- Easy creation of new projectile types through configuration
- Pluggable strategy system for custom behaviors
- Clear extension points for new features
- Runtime behavior composition

### 3. Better Integration
- Unified API for all systems
- Consistent enemy system integration
- Simplified radar and effect integration
- Cleaner external dependencies

### 4. Improved Maintainability
- Clear separation of concerns
- Reduced code duplication
- Easier testing and debugging
- Better documentation and understanding

### 5. Performance Preservation
- Maintain existing job system integration
- Keep object pooling optimizations
- Preserve burst compilation benefits
- Add new optimization opportunities

## Risk Mitigation

### 1. Performance Regression
- **Risk**: New system might be slower than current optimized system
- **Mitigation**: Incremental migration, continuous performance testing, maintain job system integration

### 2. Feature Loss
- **Risk**: Some current functionality might be lost during migration
- **Mitigation**: Comprehensive feature inventory, parallel implementation, extensive testing

### 3. Integration Breakage
- **Risk**: External systems might break during migration
- **Mitigation**: Maintain compatibility layers, gradual migration, thorough integration testing

### 4. Development Time
- **Risk**: Migration might take longer than expected
- **Mitigation**: Phased approach, clear milestones, parallel development where possible

## Success Metrics

### 1. Code Quality
- Reduce total lines of code by 20%
- Reduce number of manager classes by 50%
- Improve code maintainability scores

### 2. Performance
- Maintain current projectile count capabilities (1000+)
- Preserve frame rate performance
- Reduce memory allocation

### 3. Extensibility
- Create 3 new projectile types using new system
- Demonstrate 50% reduction in time to create new projectile variants
- Improve developer experience scores

### 4. Integration
- Seamless enemy system integration
- Simplified radar integration
- Reduced external dependencies

## Conclusion

The proposed projectile system restructuring follows the successful patterns from the enemy system migration:
- **Entity-component architecture** for flexibility
- **Strategy pattern** for behavior variation
- **Centralized configuration** for ease of use
- **Simplified management** for maintainability

This approach will reduce the current complexity while maintaining performance and significantly improving extensibility for future projectile types and behaviors.
